import React, { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "expo-router";
import AddressSelection from "@/components/organisms/AddressSelection";
import CartFooter from "@/components/organisms/CartFooter";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { useTranslation } from "react-i18next";
import { VendorDetails } from "@/types/vendor";
import Toast from "react-native-toast-message";
import { View } from "react-native";
import { useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";

interface AddressStepProps {
  onBack: () => void;
  onContinue: () => void;
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  setSelectedBillingAddressId?: (id: number | null) => void;
  setSelectedShippingAddressId?: (id: number | null) => void;
  setUseBillingAsShipping?: (value: boolean) => void;
  selectedVendor: VendorDetails;
}

const AddressStep: React.FC<AddressStepProps> = ({
  onBack,
  onContinue,
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  setSelectedBillingAddressId: externalSetBillingId,
  setSelectedShippingAddressId: externalSetShippingId,
  setUseBillingAsShipping: externalSetUseBillingAsShipping,
  selectedVendor,
}) => {
  const router = useRouter();
  const addressManagement = useAddressManagement();
  const { t } = useTranslation();
  const user = useAppSelector((state) => state.auth.user);
  const { addressList } = useAppSelector((state) => state.auth);
  const hasFetchedAddresses = useRef(false);
  const [hasStartedFetching, setHasStartedFetching] = useState(false);

  const isSalesperson = user?.role_id === UserType.SALESPERSON;

  // Memoize the fetch function to prevent infinite re-renders
  const fetchVendorAddresses = useCallback(() => {
    if (selectedVendor && isSalesperson && !hasFetchedAddresses.current) {
      hasFetchedAddresses.current = true;
      setHasStartedFetching(true);
      addressManagement.fetchAddressList({ user_id: selectedVendor.user_id });
    }
  }, [selectedVendor?.vendor_id, isSalesperson]);

  useEffect(() => {
    fetchVendorAddresses();
  }, [fetchVendorAddresses]);

  // Reset the ref when vendor changes
  useEffect(() => {
    hasFetchedAddresses.current = false;
    setHasStartedFetching(false);
  }, [selectedVendor?.vendor_id]);

  // Use external state if provided, otherwise fall back to hook state
  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  // Use external setters if provided, otherwise fall back to hook handlers
  const handleBillingAddressSelection = externalSetBillingId
    ? (address: any) => externalSetBillingId(address.id)
    : addressManagement.handleBillingAddressSelection;

  const handleShippingAddressSelection = externalSetShippingId
    ? (address: any) => externalSetShippingId(address.id)
    : addressManagement.handleShippingAddressSelection;

  const handleToggleBillingAsShipping = externalSetUseBillingAsShipping
    ? (value: boolean) => externalSetUseBillingAsShipping(value)
    : addressManagement.handleToggleBillingAsShipping;

  const {
    handleEditAddress,
    getAddresses,
    isLoadingAddresses,
    hasMoreAddresses,
    loadMoreAddresses,
  } = addressManagement;

  // For salesperson, use addresses from addressManagement (fetched for vendor)
  // For other roles, use addresses from authSlice (user's own addresses)
  const addresses = useCallback(() => {
    return isSalesperson
      ? getAddresses()
      : {
          all: Array.isArray(addressList) ? addressList : [],
          billing: [],
          shipping: [],
        };
  }, [isSalesperson, getAddresses, addressList])();

  const hasNoAddresses = addresses.all.length === 0;

  // Only show loading if we're a salesperson, have started fetching, and are still loading
  const shouldShowLoading =
    isSalesperson && hasStartedFetching && isLoadingAddresses;

  const getValidationStatus = useCallback(() => {
    if (hasNoAddresses) {
      return {
        isValid: true,
        message: t("complaint.address_validation.no_addresses"),
      };
    }

    const hasBillingAddress = !!selectedBillingAddressId;
    const hasShippingAddress = !!selectedShippingAddressId;

    if (!hasBillingAddress) {
      return {
        isValid: false,
        message: t("complaint.address_validation.billing_required"),
      };
    }

    if (!useBillingAsShipping && !hasShippingAddress) {
      return {
        isValid: false,
        message: t("complaint.address_validation.shipping_required"),
      };
    }

    return { isValid: true, message: "" };
  }, [
    hasNoAddresses,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    t,
  ]);

  const validationStatus = getValidationStatus();

  const handleContinueAction = useCallback(() => {
    if (hasNoAddresses) {
      if (!!selectedVendor) {
        router.push(
          `/(protected)/add-address?vendorId=${selectedVendor?.vendor_id}&userId=${selectedVendor?.user_id}`
        );
      } else {
        router.push(`/(protected)/add-address?userId=${user?.id}`);
      }
    } else if (!validationStatus.isValid) {
      Toast.show({
        type: "error",
        text1: t("validation.error"),
        text2: validationStatus.message,
      });
      return;
    } else {
      onContinue();
    }
  }, [hasNoAddresses, router, onContinue, selectedVendor, validationStatus]);

  return (
    <>
      <View style={{ paddingHorizontal: 16, flex: 1 }}>
        <AddressSelection
          addresses={addresses.all}
          selectedBillingId={selectedBillingAddressId}
          selectedShippingId={selectedShippingAddressId}
          onSelectBilling={handleBillingAddressSelection}
          onSelectShipping={handleShippingAddressSelection}
          onEditAddress={handleEditAddress}
          useBillingAsShipping={useBillingAsShipping}
          onToggleBillingAsShipping={handleToggleBillingAsShipping}
          selectedVendor={selectedVendor}
          isLoading={shouldShowLoading}
          hasMoreAddresses={isSalesperson ? hasMoreAddresses : false}
          onLoadMore={
            isSalesperson
              ? () => loadMoreAddresses(selectedVendor?.vendor_id)
              : undefined
          }
        />
      </View>

      <CartFooter
        onClearCart={onBack}
        onContinue={handleContinueAction}
        clearButtonTitle={t("cart.payment.buttons.back")}
        continueButtonTitle={
          hasNoAddresses
            ? t("addressSelection.addNewAddress")
            : t("cart.payment.buttons.continue")
        }
        disabled={!validationStatus.isValid || shouldShowLoading}
      />
    </>
  );
};

export default AddressStep;
