import React, { Fragment, useEffect, useRef } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { StepInfo, StepText, Separator, StepInfoContainer } from "./styles";
import { Spacer } from "@/styles/common.style";
import { useTheme } from "@/hooks/useTheme";
import { Dimensions, View, ScrollView } from "react-native";
import { OrderStatus } from "@/types/order";
const { width } = Dimensions.get("screen");

export interface StepperProps {
  stepData: OrderStatus[];
  currentId: number;
  setSelectedTabNav?: (id: number) => void;
  allowFutureStepNavigation?: boolean;
  getStepValidation?: (stepId: number) => boolean;
}

const CurrentStep = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons
      name="radio-button-checked"
      size={24}
      color={theme.colors.primary}
    />
  );
};

const NextStep = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons
      name="radio-button-unchecked"
      size={24}
      color={theme.colors.gray}
    />
  );
};

const CheckCircle = () => {
  const { theme } = useTheme();
  return (
    <MaterialIcons name="check-circle" size={24} color={theme.colors.success} />
  );
};

const Stepper: React.FC<StepperProps> = ({
  stepData,
  currentId,
  setSelectedTabNav,
  allowFutureStepNavigation = false,
  getStepValidation,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const stepRefs = useRef<{ [key: number]: View | null }>({});

  const currentStepIndex = stepData.findIndex((step) => step.id === currentId);

  useEffect(() => {
    if (scrollViewRef.current && stepRefs.current[currentId]) {
      stepRefs.current[currentId]?.measureLayout(
        scrollViewRef.current,
        (x: number) => {
          scrollViewRef.current?.scrollTo({
            x: x - width / 4,
            animated: true,
          });
        },
        () => {}
      );
    }
  }, [currentId]);

  const renderIcon = (stepIndex: number) => {
    if (stepIndex === currentStepIndex) return <CurrentStep />;
    if (stepIndex < currentStepIndex) return <CheckCircle />;
    return <NextStep />;
  };

  const stepWidth = width / stepData.length - 12 * 5;

  const isStepAccessible = (stepIndex: number, stepId: number) => {
    if (allowFutureStepNavigation) return true;

    // Always allow navigation to current step or previous steps
    if (stepIndex <= currentStepIndex) return true;

    // For future steps, check if all previous steps are valid
    if (getStepValidation) {
      for (let i = 0; i < stepIndex; i++) {
        const step = stepData[i];
        if (!getStepValidation(step.id)) {
          return false;
        }
      }
      return true;
    }

    return false;
  };

  const handleStepPress = (stepId: number, stepIndex: number) => {
    if (isStepAccessible(stepIndex, stepId)) {
      setSelectedTabNav?.(stepId);
    }
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
      scrollEnabled={stepData.length > 3}
    >
      {stepData.map((item, index) => (
        <Fragment key={item.id}>
          <StepInfoContainer
            onPress={() => handleStepPress(item.id, index)}
            ref={(ref) => (stepRefs.current[item.id] = ref)}
            disabled={!isStepAccessible(index, item.id)}
          >
            <StepInfo>
              {renderIcon(index)}
              <Spacer size={4} />
              <StepText
                numberOfLines={2}
                isCurrentStep={index === currentStepIndex}
                isAccessible={isStepAccessible(index, item.id)}
              >
                {item.label}
              </StepText>
            </StepInfo>
          </StepInfoContainer>
          {index < stepData.length - 1 && <Separator width={stepWidth - 15} />}
        </Fragment>
      ))}
    </ScrollView>
  );
};

export default Stepper;
