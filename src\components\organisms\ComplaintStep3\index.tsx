import React, { useCallback, useEffect, useState, useRef } from "react";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON><PERSON>r,
  StepHeader,
  StepTitle,
  StepSubtitle,
} from "@/styles/Complaint.styles";
import AddressSelection from "@/components/organisms/AddressSelection";
import { useAddressManagement } from "@/hooks/useAddressManagement";
import { VendorDetails } from "@/types/vendor";
import { Customer } from "@/types/customer";
import { useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";

interface ComplaintStep3Props {
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  setSelectedBillingAddressId?: (id: number | null) => void;
  setSelectedShippingAddressId?: (id: number | null) => void;
  setUseBillingAsShipping?: (value: boolean) => void;
  selectedVendor?: VendorDetails | null;
  selectedCustomer?: Customer | null;
}

const ComplaintStep3: React.FC<ComplaintStep3Props> = ({
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  setSelectedBillingAddressId: externalSetBillingId,
  setSelectedShippingAddressId: externalSetShippingId,
  setUseBillingAsShipping: externalSetUseBillingAsShipping,
  selectedVendor,
  selectedCustomer,
}) => {
  const router = useRouter();
  const addressManagement = useAddressManagement();
  const { t } = useTranslation();
  const { user, addressList } = useAppSelector((state) => state.auth);
  const hasFetchedAddresses = useRef(false);
  const [hasStartedFetching, setHasStartedFetching] = useState(false);

  const isSalesperson = user?.role_id === UserType.SALESPERSON;

  // Fetch addresses for the selected entity (vendor/customer) when needed
  const fetchEntityAddresses = useCallback(() => {
    if (isSalesperson && !hasFetchedAddresses.current) {
      let entityUserId: number | undefined;

      if (selectedVendor) {
        entityUserId = selectedVendor.user_id;
      } else if (selectedCustomer) {
        entityUserId = selectedCustomer.user_id;
      }

      if (entityUserId) {
        hasFetchedAddresses.current = true;
        setHasStartedFetching(true);
        addressManagement.fetchAddressList({ user_id: entityUserId });
      }
    }
  }, [selectedVendor?.user_id, selectedCustomer?.user_id, isSalesperson, addressManagement]);

  useEffect(() => {
    fetchEntityAddresses();
  }, [fetchEntityAddresses]);

  // Reset the ref when entity changes
  useEffect(() => {
    hasFetchedAddresses.current = false;
    setHasStartedFetching(false);
  }, [selectedVendor?.user_id, selectedCustomer?.user_id]);

  // Use external state if provided, otherwise fall back to hook state
  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  // Use external setters if provided, otherwise fall back to hook handlers
  const handleBillingAddressSelection = externalSetBillingId
    ? (address: any) => externalSetBillingId(address.id)
    : addressManagement.handleBillingAddressSelection;

  const handleShippingAddressSelection = externalSetShippingId
    ? (address: any) => externalSetShippingId(address.id)
    : addressManagement.handleShippingAddressSelection;

  const handleToggleBillingAsShipping = externalSetUseBillingAsShipping
    ? (value: boolean) => {
        externalSetUseBillingAsShipping(value);
        // Clear shipping address selection when using billing as shipping
        if (value && externalSetShippingId) {
          externalSetShippingId(null);
        }
      }
    : addressManagement.handleToggleBillingAsShipping;

  const {
    handleEditAddress,
    getAddresses,
    isLoadingAddresses,
    hasMoreAddresses,
    loadMoreAddresses,
  } = addressManagement;

  // For salesperson, use addresses from addressManagement (fetched for entity)
  // For other roles, use addresses from authSlice (user's own addresses)
  const addresses = useCallback(() => {
    return isSalesperson
      ? getAddresses()
      : {
          all: Array.isArray(addressList) ? addressList : [],
          billing: [],
          shipping: [],
        };
  }, [isSalesperson, getAddresses, addressList])();

  const hasNoAddresses = addresses.all.length === 0;

  // Only show loading if we're a salesperson, have started fetching, and are still loading
  const shouldShowLoading =
    isSalesperson && hasStartedFetching && isLoadingAddresses;

  // Set default addresses when addresses are available and external setters are provided
  useEffect(() => {
    if (externalSetBillingId && externalSetShippingId && addresses.all.length > 0) {
      // Only set defaults if no addresses are currently selected
      if (!selectedBillingAddressId && !selectedShippingAddressId) {
        // Set default billing address
        const billingAddresses = addresses.all.filter(
          (addr: any) => addr.billing_shipping === "1"
        );
        if (billingAddresses.length > 0) {
          externalSetBillingId(billingAddresses[0].id);
        }

        // Set default shipping address if not using billing as shipping
        if (!useBillingAsShipping) {
          const shippingAddresses = addresses.all.filter(
            (addr: any) => addr.billing_shipping === "2"
          );
          if (shippingAddresses.length > 0) {
            externalSetShippingId(shippingAddresses[0].id);
          }
        }
      }
    }
  }, [
    addresses.all,
    selectedBillingAddressId,
    selectedShippingAddressId,
    useBillingAsShipping,
    externalSetBillingId,
    externalSetShippingId,
  ]);

  const handleAddNewAddress = () => {
    if (selectedVendor) {
      router.push(
        `/(protected)/add-address?vendorId=${selectedVendor.vendor_id}&userId=${selectedVendor.user_id}`
      );
    } else if (selectedCustomer) {
      router.push(
        `/(protected)/add-address?customerId=${selectedCustomer.user_id}&userId=${selectedCustomer.user_id}`
      );
    } else {
      router.push(`/(protected)/add-address?userId=${user?.id}`);
    }
  };

  const getEntityName = () => {
    if (selectedVendor) {
      return (
        selectedVendor.name ||
        selectedVendor.first_name + " " + selectedVendor.last_name
      );
    } else if (selectedCustomer) {
      return (
        selectedCustomer.name ||
        selectedCustomer.first_name + " " + selectedCustomer.last_name
      );
    }
    return t("complaint.form.current_user");
  };

  if (hasNoAddresses) {
    return (
      <StepContainer>
        <StepHeader>
          <StepTitle>{t("complaint.form.select_address")}</StepTitle>
        </StepHeader>

        <AddressSelection
          addresses={addresses.all}
          selectedBillingId={selectedBillingAddressId}
          selectedShippingId={selectedShippingAddressId}
          onSelectBilling={handleBillingAddressSelection}
          onSelectShipping={handleShippingAddressSelection}
          onEditAddress={handleEditAddress}
          useBillingAsShipping={useBillingAsShipping}
          onToggleBillingAsShipping={handleToggleBillingAsShipping}
          selectedVendor={selectedVendor}
          onAddNewAddress={handleAddNewAddress}
          isLoading={shouldShowLoading}
          hasMoreAddresses={isSalesperson ? hasMoreAddresses : false}
          onLoadMore={
            isSalesperson
              ? () => {
                  const entityUserId = selectedVendor?.user_id || selectedCustomer?.user_id;
                  if (entityUserId) {
                    loadMoreAddresses(entityUserId);
                  }
                }
              : undefined
          }
        />
      </StepContainer>
    );
  }

  return (
    <StepContainer>
      <StepHeader>
        <StepTitle>{t("complaint.form.select_address")}</StepTitle>
        <StepSubtitle>
          {t("complaint.form.choose_complaint_address_for_entity", {
            entity: getEntityName(),
          })}
        </StepSubtitle>
      </StepHeader>

      <AddressSelection
        addresses={addresses.all}
        selectedBillingId={selectedBillingAddressId}
        selectedShippingId={selectedShippingAddressId}
        onSelectBilling={handleBillingAddressSelection}
        onSelectShipping={handleShippingAddressSelection}
        onEditAddress={handleEditAddress}
        useBillingAsShipping={useBillingAsShipping}
        onToggleBillingAsShipping={handleToggleBillingAsShipping}
        selectedVendor={selectedVendor}
        onAddNewAddress={handleAddNewAddress}
        isLoading={shouldShowLoading}
        hasMoreAddresses={isSalesperson ? hasMoreAddresses : false}
        onLoadMore={
          isSalesperson
            ? () => {
                const entityUserId = selectedVendor?.user_id || selectedCustomer?.user_id;
                if (entityUserId) {
                  loadMoreAddresses(entityUserId);
                }
              }
            : undefined
        }
      />
    </StepContainer>
  );
};

export default ComplaintStep3;
